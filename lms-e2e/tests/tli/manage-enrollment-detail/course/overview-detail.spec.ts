import { expect } from '@playwright/test';
import { v4 as uuidv4 } from 'uuid';
import { test } from '../../../../fixtures/default-fixture';
import { RoundsRepo } from '../../../../../shared/repositories/lms/repo/rounds-repo/round-repo';
import { UsersRepo } from '../../../../../shared/repositories/lms/repo/users-repo/users-repo';
import { EnrollmentsRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/enrollments-repo';
import { EnrollmentCertificatesRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/enrollment-certificates-repo';
import { EnrollmentAttachmentsRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/enrollment-attachments-repo';
import { QuizAnswersRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/quiz-answers-repo';
import { PreEnrollmentTransactionRepo } from '../../../../../shared/repositories/lms/repo/pre-enrollments-repo/pre-enrollment-transaction-repo';
import { EnrollType } from '../../../../../shared/repositories/lms/constants/enums/enroll-type.enum';
import { CertificatePage } from '../../../../elements/pages/regular-site/certificate-page';
import { ObjectiveCourse } from '../../../../elements/pages/backoffice/create-course-elements';
import { ApplicantType, LicenseRenewal } from '../../../../elements/pages/backoffice/general-course-detail-elements';
import { CourseStatus, MediaType } from '../../../../elements/pages/backoffice/curriculum/manage-curriculum-page';

let resetTimestamp: number;

async function cleanUpRoundAndEnrollment(
  repositories: {
    roundRepo: RoundsRepo;
    usersRepo: UsersRepo;
    enrollmentsRepo: EnrollmentsRepo;
    enrollmentCertificatesRepo: EnrollmentCertificatesRepo;
    enrollmentAttachmentsRepo: EnrollmentAttachmentsRepo;
    quizAnswersRepo: QuizAnswersRepo;
    preEnrollmentTransactionRepo: PreEnrollmentTransactionRepo;
  },
  email: string,
  roundDate: Date,
  course: string,
) {
  const lmsUser = await repositories.usersRepo.getByEmail(email);
  const userEnrollments = await repositories.enrollmentsRepo.getAllEnrollmentsForUser(lmsUser.guid);

  // Clean up round
  await repositories.roundRepo.deleteTrainingRoundByTrainingDateAndCourseId(course, roundDate);

  // Clean up enrollment
  for (const userEnrollment of userEnrollments) {
    await repositories.enrollmentCertificatesRepo.deleteAllEnrollmentCertificatesForEnrollment(userEnrollment.id);
    await repositories.enrollmentAttachmentsRepo.deleteAllEnrollmentAttachedForEnrollment(userEnrollment.id);
    await repositories.quizAnswersRepo.deleteManyByEnrollmentId(userEnrollment.id);
  }
  await repositories.enrollmentsRepo.deleteAllEnrollmentsForUser(lmsUser.guid);

  // Clean Pre-Enrollment Transaction
  await repositories.preEnrollmentTransactionRepo.deleteByPayloadEmail(lmsUser.email);

  // Clean user return URL
  await repositories.usersRepo.clearUserReturnUrl(lmsUser.email);
  resetTimestamp = new Date().getTime();
}

test.beforeEach(
  async ({
    roundRepo,
    configuration,
    usersRepo,
    enrollmentsRepo,
    enrollmentCertificatesRepo,
    enrollmentAttachmentsRepo,
    quizAnswersRepo,
    preEnrollmentTransactionRepo,
  }) => {
    const courses = [
      configuration.shareCourses.courseOICAdditionalDoc,
      configuration.shareCourses.courseRegularAdditionalDoc,
      configuration.shareCourses.oicAutoApproveCourse,
      configuration.shareCourses.oicCourseManualApprove,
    ];
    const lmsUsers = [
      configuration.shareUsers.userSendDocument,
      configuration.shareUsers.userTLIMatchCitizenCard,
      configuration.shareUsers.userCommentTLI,
    ];

    const currentDate = new Date();
    const roundDate = new Date(new Date().setDate(currentDate.getDate() + 2));
    roundDate.setHours(0, 0, 0, 0);

    // Clean data enrollment and round
    for (const course of courses) {
      for (const user of lmsUsers) {
        await cleanUpRoundAndEnrollment(
          {
            roundRepo,
            usersRepo,
            enrollmentsRepo,
            enrollmentCertificatesRepo,
            enrollmentAttachmentsRepo,
            quizAnswersRepo,
            preEnrollmentTransactionRepo,
          },
          user.email,
          roundDate,
          course.courseId,
        );
      }
    }
  },
);

test.afterEach(
  async ({
    roundRepo,
    configuration,
    usersRepo,
    enrollmentsRepo,
    enrollmentCertificatesRepo,
    enrollmentAttachmentsRepo,
    quizAnswersRepo,
    preEnrollmentTransactionRepo,
  }) => {
    const courses = [
      configuration.shareCourses.courseOICAdditionalDoc,
      configuration.shareCourses.courseRegularAdditionalDoc,
      configuration.shareCourses.oicAutoApproveCourse,
      configuration.shareCourses.oicCourseManualApprove,
    ];
    const lmsUsers = [
      configuration.shareUsers.userSendDocument,
      configuration.shareUsers.userTLIMatchCitizenCard,
      configuration.shareUsers.userCommentTLI,
    ];
    const currentDate = new Date();
    const roundDate = new Date(new Date().setDate(currentDate.getDate() + 2));
    roundDate.setHours(0, 0, 0, 0);

    for (const course of courses) {
      for (const user of lmsUsers) {
        await cleanUpRoundAndEnrollment(
          {
            roundRepo,
            usersRepo,
            enrollmentsRepo,
            enrollmentCertificatesRepo,
            enrollmentAttachmentsRepo,
            quizAnswersRepo,
            preEnrollmentTransactionRepo,
          },
          user.email,
          roundDate,
          course.courseId,
        );
      }
    }
  },
);

test.describe('Admin - Manage enrollment course detail', () => {
  test('@SKL-T20021 Admin able to request additional documents and review documents after user submitted for OIC', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    trainingEnrollmentsPage,
    attachmentPage,
    additionalDocumentsPage,
    roundRepo,
    enrollmentsRepo,
    testmailAppClient,
    myCoursePage,
  }) => {
    const userSendDocument = configuration.shareUsers.userSendDocument;
    const course = configuration.shareCourses.courseOICAdditionalDoc;
    const oicCourseVersions = configuration.courseVersions.oicCourse2Version1;
    const adminRequestDocument = configuration.shareUsers.adminRequestDocument;

    const currentDate = new Date();
    const roundDate = currentDate;
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(23, 59, 0, 0);
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const selectCalendar = new Date(new Date().setMonth(currentDate.getMonth() + 1));

    // Seed round
    const round = await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: currentDate,
      updatedAt: currentDate,
      courseIds: oicCourseVersions.courseId,
      organizationId: userSendDocument.organizationId,
    });

    // Seed enrollment course OIC
    await enrollmentsRepo.create({
      id: uuidv4(),
      userId: userSendDocument.guid,
      status: EnrollmentsRepo.STATUS.inProgress,
      createdAt: currentDate,
      updatedAt: currentDate,
      startedAt: currentDate,
      expiredAt: expiredAt,
      completedCourseItem: 0,
      learningProgress: [],
      business: EnrollmentsRepo.BUSINESS.b2c,
      roundId: round.id,
      isCountdownArticle: false,
      isIdentityVerificationEnabled: true,
      organizationId: userSendDocument.organizationId,
      courseId: course.courseId,
      courseVersionId: oicCourseVersions.id,
      approvalAt: null,
      acceptedAt: null,
    });

    // Admin Login Request Additional Document OIC
    await loginPage.loginWithUsernameAndPassword(adminRequestDocument.username, adminRequestDocument.password);
    await adminDashboardPage.accessManageOICEnrollment();
    await trainingEnrollmentsPage.clickEnrollmentLearningTab();
    await trainingEnrollmentsPage.searchUsers(userSendDocument.firstname + ' ' + userSendDocument.lastname);
    await trainingEnrollmentsPage.viewEnrollmentDetail();
    await expect(trainingEnrollmentsPage.verifyFullNameLocator).toBeVisible();
    await trainingEnrollmentsPage.requestDocument('ขาดเอกสารบัตรประชาชนยืนยันตน', selectCalendar);
    await homePage.logout();

    //User receive email
    const email = await testmailAppClient.fetchLatestEmailInbox(
      userSendDocument.email,
      '*ขอเอกสารเพิ่มเติมสำหรับหลักสูตร*',
      resetTimestamp,
    );
    expect(email.html).toContain('ขอเอกสารเพิ่มเติม');

    // User Login Check List Document
    await loginPage.loginWithUsernameAndPassword(userSendDocument.username, userSendDocument.password);
    await myCoursePage.verifyNotification(
      `ขอให้คุณส่งเอกสารเพิ่มเติมเพื่อประกอบการพิจารณาอนุมัติการอบรม สำหรับหลักสูตร \"${course.name}\"`,
    ),
      await attachmentPage.viewListDocument(userSendDocument.citizenId);
    await attachmentPage.uploadFileDocument(configuration.ekycs.uploadProfile);
    await attachmentPage.fillInformation();
    await attachmentPage.confirmInformation();
    await homePage.logout();

    // Admin Login Verify Additional Document
    await loginPage.loginWithUsernameAndPassword(adminRequestDocument.username, adminRequestDocument.password);
    await adminDashboardPage.accessManageAdditionalDocuments();
    await additionalDocumentsPage.viewUserAdditionalDocumentDetail(
      userSendDocument.firstname + ' ' + userSendDocument.lastname,
    );
    await additionalDocumentsPage.approveDocument();
  });

  test('@SKL-T20021 Admin able to request additional documents and review documents after user submitted for Regular', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    regularEnrollmentPage,
    attachmentPage,
    additionalDocumentsPage,
    roundRepo,
    enrollmentsRepo,
    testmailAppClient,
    myCoursePage,
  }) => {
    const userSendDocument = configuration.shareUsers.userSendDocument;
    const course = configuration.shareCourses.courseRegularAdditionalDoc;
    const regularCourseVersions = configuration.courseVersions.regularCourse2Version1;
    const adminRequestDocument = configuration.shareUsers.adminRequestDocument;

    const currentDate = new Date();
    const passedDate = new Date(new Date().setDate(currentDate.getMonth() - 2));

    const expiredAt = new Date(new Date().setMonth(currentDate.getMonth() + 1));
    const passedRoundDate = new Date(new Date().setMonth(currentDate.getMonth() - 0));
    const firstRegisDate = new Date(new Date().setMonth(currentDate.getMonth() - 3));
    const lastRegisDate = new Date(new Date().setMonth(currentDate.getMonth() - 2));

    const selectCalendar = new Date(new Date().setMonth(currentDate.getMonth() + 1));

    passedRoundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(23, 59, 0, 0);

    // Seed round passed
    const round = await roundRepo.create({
      id: uuidv4(),
      roundDate: passedRoundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      courseIds: regularCourseVersions.courseId,
      createdAt: currentDate,
      updatedAt: currentDate,
    });

    // Seed enrollment regular passed
    await enrollmentsRepo.create({
      id: uuidv4(),
      userId: userSendDocument.guid,
      status: EnrollmentsRepo.STATUS.inProgress,
      createdAt: passedDate,
      updatedAt: passedDate,
      startedAt: passedRoundDate,
      expiredAt: expiredAt,
      completedCourseItem: 0,
      learningProgress: [],
      business: EnrollmentsRepo.BUSINESS.b2c,
      roundId: round.id,
      isCountdownArticle: false,
      isIdentityVerificationEnabled: true,
      organizationId: userSendDocument.organizationId,
      courseId: course.courseId,
      courseVersionId: regularCourseVersions.id,
      approvalAt: null,
      acceptedAt: null,
    });

    // Admin Login Request Additional Document Regular
    await loginPage.loginWithUsernameAndPassword(adminRequestDocument.username, adminRequestDocument.password);
    await adminDashboardPage.accessManageRegularEnrollment();
    await regularEnrollmentPage.clickEnrollmentLearningTab();
    await regularEnrollmentPage.searchUsers(userSendDocument.firstname + ' ' + userSendDocument.lastname);
    await regularEnrollmentPage.viewEnrollmentDetail();
    await expect(regularEnrollmentPage.verifyFullNameLocator).toBeVisible();
    await regularEnrollmentPage.requestDocument('ขาดเอกสารบัตรประชาชนยืนยันตน', selectCalendar);
    await homePage.logout();

    //User receive email
    const email = await testmailAppClient.fetchLatestEmailInbox(
      userSendDocument.email,
      '*ขอเอกสารเพิ่มเติมสำหรับหลักสูตร*',
      resetTimestamp,
    );
    expect(email.html).toContain('ขอเอกสารเพิ่มเติม');

    // User Login
    await loginPage.loginWithUsernameAndPassword(userSendDocument.username, userSendDocument.password);
    await myCoursePage.verifyNotification(
      `ขอให้คุณส่งเอกสารเพิ่มเติมเพื่อประกอบการพิจารณาอนุมัติการอบรม สำหรับหลักสูตร \"${course.name}\"`,
    ),
      await attachmentPage.viewListDocument(userSendDocument.citizenId);
    await attachmentPage.uploadFileDocument(configuration.ekycs.uploadProfile);
    await attachmentPage.fillInformation();
    await attachmentPage.confirmInformation();
    await homePage.logout();

    // Admin Login Verify Additional Document
    await loginPage.loginWithUsernameAndPassword(adminRequestDocument.username, adminRequestDocument.password);
    await adminDashboardPage.accessManageAdditionalDocuments();
    await additionalDocumentsPage.viewUserAdditionalDocumentDetail(
      userSendDocument.firstname + ' ' + userSendDocument.lastname,
    );
    await additionalDocumentsPage.approveDocument();
    await homePage.logout();
  });

  test('@SKL-T20022 Admin able to edit remark on enrollment detail', async ({
    configuration,
    loginPage,
    adminDashboardPage,
    trainingEnrollmentsPage,
    roundRepo,
    enrollmentsRepo,
  }) => {
    const learnerTLI = configuration.shareUsers.userSendDocument;
    const course = configuration.shareCourses.courseOICAdditionalDoc;
    const oicCourseVersions = configuration.courseVersions.oicCourse2Version1;
    const adminEditRemark = configuration.shareUsers.adminRequestDocument;

    const currentDate = new Date();
    const roundDate = currentDate;
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(23, 59, 0, 0);
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);

    // Seed round
    const round = await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: currentDate,
      updatedAt: currentDate,
      courseIds: oicCourseVersions.courseId,
      organizationId: learnerTLI.organizationId,
    });

    // Seed enrollment course OIC
    await enrollmentsRepo.create({
      id: uuidv4(),
      userId: learnerTLI.guid,
      status: EnrollmentsRepo.STATUS.inProgress,
      createdAt: currentDate,
      updatedAt: currentDate,
      startedAt: currentDate,
      expiredAt: expiredAt,
      completedCourseItem: 0,
      learningProgress: [],
      business: EnrollmentsRepo.BUSINESS.b2b,
      enrollType: EnrollType.voluntary,
      roundId: round.id,
      isCountdownArticle: false,
      isIdentityVerificationEnabled: true,
      organizationId: learnerTLI.organizationId,
      courseId: course.courseId,
      courseVersionId: oicCourseVersions.id,
      approvalAt: null,
      acceptedAt: null,
      externalContentType: '',
    });

    // Admin Login Request Additional Document OIC
    await loginPage.loginWithUsernameAndPassword(adminEditRemark.username, adminEditRemark.password);
    await adminDashboardPage.accessManageOICEnrollment();
    await trainingEnrollmentsPage.clickEnrollmentLearningTab();
    await trainingEnrollmentsPage.searchUsers(learnerTLI.firstname + ' ' + learnerTLI.lastname);
    await trainingEnrollmentsPage.viewEnrollmentDetail();
    await expect(trainingEnrollmentsPage.verifyFullNameLocator).toBeVisible();

    const remarkText = 'ทดสอบหมายเหตุ';
    await trainingEnrollmentsPage.editRemark(remarkText);

    // Verify that the remark was saved and displays correctly
    await trainingEnrollmentsPage.verifyRemarkText(remarkText);
  });

  test('@SKL-T20023 Admin able to edit ID card image and face image on enrollment detail', async ({
    configuration,
    loginPage,
    adminDashboardPage,
    enrollmentsRepo,
    roundRepo,
    trainingEnrollmentsPage,
    homePage,
    myCoursePage,
    verifyIdentityPage,
    page,
  }) => {
    const course = configuration.shareCourses.oicAutoApproveCourse;
    const userTLI = configuration.shareUsers.userTLIMatchCitizenCard;
    const organizationAdminTLI = configuration.usersLocal.organizationAdminTLI1;
    const images = configuration.imageFaceCompare;

    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const roundDate = new Date();
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    const uniqueRoundId = uuidv4();
    const uniqueEnrollmentId = uuidv4();

    // Seed round
    const round = await roundRepo.create({
      id: uniqueRoundId,
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: date,
      updatedAt: date,
      courseIds: [course.courseId],
      organizationId: userTLI.organizationId,
    });

    // Seed enrollment
    const enrollment = await enrollmentsRepo.create({
      id: uniqueEnrollmentId,
      courseId: course.courseId,
      courseVersionId: course.courseVersions[1].id,
      organizationId: userTLI.organizationId,
      userId: userTLI.userId,
      business: EnrollmentsRepo.BUSINESS.b2c,
      status: EnrollmentsRepo.STATUS.inProgress,
      isCountdownArticle: false,
      roundId: round.id,
      completedCourseItem: 0,
      learningProgress: [],
      startedAt: date,
      expiredAt: expiredAt,
      acceptedAt: null,
      createdAt: date,
      updatedAt: date,
      isIdentityVerificationEnabled: true,
      remark: '',
      externalContentType: '',
      enrollType: EnrollType.voluntary,
      customerCode: '',
      imageIdCardPath: images.imageFileAfterUpload.imageIdCardPath,
      imageFacePath: images.imageFileAfterUpload.imageFacePath,
    });

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdminTLI.username, organizationAdminTLI.password);
    await adminDashboardPage.accessManageOICEnrollment();
    await trainingEnrollmentsPage.clickEnrollmentLearningTab();
    await trainingEnrollmentsPage.searchUsers(userTLI.firstname + ' ' + userTLI.lastname);
    await trainingEnrollmentsPage.viewEnrollmentDetail();

    await trainingEnrollmentsPage.deleteIdCardImage(organizationAdminTLI.email);
    await trainingEnrollmentsPage.uploadIdCardImage(configuration.ekycs.validIdCardImagePath2);
    await trainingEnrollmentsPage.verifyValidIdCardImage();

    await trainingEnrollmentsPage.deleteFaceImage(organizationAdminTLI.email);
    await trainingEnrollmentsPage.uploadFaceImage(configuration.ekycs.faceImagePath);
    await trainingEnrollmentsPage.verifyValidFaceImage();

    // log out and login learner
    await homePage.logout();
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();

    // verify image id card and face image
    await myCoursePage.clickToMyCourse();
    await myCoursePage.waitForCourseLoaded();
    await myCoursePage.continueLearning(1);
    await verifyIdentityPage.submitAgreeConsent();
    await expect(verifyIdentityPage.idCardImageLocator).not.toHaveAttribute(
      'src',
      images.imageFileAfterUpload.imageIdCardPath,
    );
  });

  test('@SKL-T20024 Admin unable to upload ID card image and face image with invalid image on enrollment detail', async ({
    configuration,
    loginPage,
    adminDashboardPage,
    enrollmentsRepo,
    roundRepo,
    trainingEnrollmentsPage,
    page,
  }) => {
    const course = configuration.shareCourses.oicAutoApproveCourse;
    const userTLI = configuration.shareUsers.userTLIMatchCitizenCard;
    const organizationAdminTLI = configuration.usersLocal.organizationAdminTLI1;

    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const roundDate = new Date();
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    const uniqueRoundId = uuidv4();
    const uniqueEnrollmentId = uuidv4();

    // Seed round
    const round = await roundRepo.create({
      id: uniqueRoundId,
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: date,
      updatedAt: date,
      courseIds: [course.courseId],
      organizationId: userTLI.organizationId,
    });

    // Seed enrollment
    await enrollmentsRepo.create({
      id: uniqueEnrollmentId,
      courseId: course.courseId,
      courseVersionId: course.courseVersions[1].id,
      organizationId: userTLI.organizationId,
      userId: userTLI.userId,
      business: EnrollmentsRepo.BUSINESS.b2c,
      status: EnrollmentsRepo.STATUS.inProgress,
      isCountdownArticle: false,
      roundId: round.id,
      completedCourseItem: 0,
      learningProgress: [],
      startedAt: date,
      expiredAt: expiredAt,
      acceptedAt: null,
      createdAt: date,
      updatedAt: date,
      isIdentityVerificationEnabled: true,
      remark: '',
      externalContentType: '',
      enrollType: EnrollType.voluntary,
      customerCode: '',
    });

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdminTLI.username, organizationAdminTLI.password);
    await adminDashboardPage.accessManageOICEnrollment();
    await trainingEnrollmentsPage.clickEnrollmentLearningTab();
    await trainingEnrollmentsPage.searchUsers(userTLI.firstname + ' ' + userTLI.lastname);
    await trainingEnrollmentsPage.viewEnrollmentDetail();

    await trainingEnrollmentsPage.uploadIdCardImage(configuration.ekycs.invalid);
    await trainingEnrollmentsPage.verifyInvalidIdCardImage('invalidImage');

    await trainingEnrollmentsPage.uploadIdCardImage(configuration.ekycs.twoPerson);
    await trainingEnrollmentsPage.verifyInvalidIdCardImage('twoPerson');

    await trainingEnrollmentsPage.uploadFaceImage(configuration.ekycs.invalid);
    await trainingEnrollmentsPage.verifyInvalidFaceImage('invalidImage');

    await trainingEnrollmentsPage.uploadFaceImage(configuration.ekycs.twoPerson);
    await trainingEnrollmentsPage.verifyInvalidFaceImage('twoPerson');
  });

  test('SKL-T20035 @SKL-T20036 Admin verify certificate when approve enrollment and able to re-generate certificate', async ({
    page,
    configuration,
    loginPage,
    homePage,
    myCoursePage,
    learningPage,
    adminDashboardPage,
    verifyIdentityPage,
    pendingApprovalPage,
    pendingApprovalDetailPage,
    enrollmentsRepo,
    enrollmentCertificatesRepo,
    roundRepo,
    testmailAppClient,
  }) => {
    const course = configuration.shareCourses.oicCourseManualApprove;
    const courseVersion = configuration.shareCourses.oicCourseManualApprove.courseVersions[1];
    const certificate = configuration.shareCourses.oicCourseManualApprove.certificate;
    const userTLI = configuration.shareUsers.userCommentTLI;
    const organizationAdminTLI = configuration.shareUsers.userAdminReplyTLI;

    // Seed round
    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);

    const roundDate = new Date();
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    const round = await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: date,
      updatedAt: date,
      courseIds: [course.courseId],
      organizationId: userTLI.organizationId,
    });

    // Seed enrollment
    const enrollment = await enrollmentsRepo.create({
      id: uuidv4(),
      courseId: course.courseId,
      courseVersionId: courseVersion.id,
      organizationId: userTLI.organizationId,
      userId: userTLI.guid,
      business: EnrollmentsRepo.BUSINESS.b2c,
      status: EnrollmentsRepo.STATUS.inProgress,
      isCountdownArticle: false,
      roundId: round.id,
      completedCourseItem: 0,
      learningProgress: [],
      startedAt: date,
      expiredAt: expiredAt,
      acceptedAt: null,
      createdAt: date,
      updatedAt: date,
      isIdentityVerificationEnabled: false,
      remark: '',
      externalContentType: '',
      enrollType: EnrollType.voluntary,
      customerCode: '',
    });

    // seed enrollment certificate
    await enrollmentCertificatesRepo.create({
      id: uuidv4(),
      courseVersionCertificateId: certificate.courseVersionCertificateId,
      enrollmentId: enrollment.id,
      slugName: certificate.slugName,
      payload: {
        logoImageUrl: '',
        tsiCode: '',
        issuedBy: '',
        pillarName: '',
      },
      pillarName: '',
      refCode: certificate.refCode,
      refName: certificate.refName,
      tsiCode: '',
      type: certificate.type,
      certificateUrl: '',
      certificateCode: '',
      logoImageUrl: '',
      issuedBy: '',
      isSentEmailOnExpiredDate: false,
      isSentEmail: false,
      createdAt: date,
      updatedAt: date,
    });

    //User login
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();

    // User learn and submit course
    await myCoursePage.clickToMyCourse();
    await myCoursePage.waitForCourseLoaded();
    await myCoursePage.continueLearning(1);
    await verifyIdentityPage.submitAgreeConsent();
    await learningPage.finishOICCourseModalLocator.waitFor({ state: 'visible' });
    await learningPage.submitLearningResultOnModal();
    await myCoursePage.waitForCourseLoaded();
    await page.waitForLoadState('load');
    expect(await myCoursePage.verifyCourseStatusByCourseName(course.name)).toContain('รออนุมัติ');
    await homePage.logout();

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdminTLI.username, organizationAdminTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();

    // Admin verify enrollment
    await adminDashboardPage.accessManagePendingApprovalOIC();
    await page.reload();
    await pendingApprovalPage.searchPendingApproval(`${userTLI.firstname} ${userTLI.lastname}`);
    await pendingApprovalDetailPage.accessPendingApprovalDetail();
    await pendingApprovalDetailPage.pendingApprovalElement.submitVerified();
    await expect(pendingApprovalDetailPage.successEditEnrollmentApprovalToastMsgLocator).toBeVisible();
    await pendingApprovalDetailPage.approvalHistoryTab.click();
    await expect(pendingApprovalDetailPage.latestApprovalHistoryStatusLocator).toContainText('ผ่านการตรวจสอบ');

    // Admin approve enrollment
    await pendingApprovalDetailPage.pendingApprovalElement.submitApproval();
    await expect(pendingApprovalDetailPage.successEditEnrollmentApprovalToastMsgLocator).toBeVisible();
    await expect(pendingApprovalDetailPage.latestApprovalHistoryStatusLocator).toContainText('อนุมัติ');

    await page.reload();
    await expect(pendingApprovalDetailPage.verifyCertificateButtonLocator).toBeVisible();

    // Handle new tab for certificate verification
    const [_certificatePage] = await Promise.all([
      page.context().waitForEvent('page'),
      pendingApprovalDetailPage.verifyCertificateButtonLocator.click(),
    ]);

    await _certificatePage.waitForLoadState();

    // Verify certificate content
    const certificatePage = new CertificatePage(_certificatePage);
    await certificatePage.verifyCertificateContent(userTLI.salute, userTLI.firstname, userTLI.lastname, course.name);
    await _certificatePage.close();

    const timestamp = new Date().getTime();
    // re-generate certificate
    await pendingApprovalDetailPage.generateCertificate();

    // verify re-generate certificate email and toast msg
    await expect(pendingApprovalDetailPage.toastSuccessReGenerateCertificateAndSendEmailLocator).toBeVisible();
    const email = await testmailAppClient.fetchLatestEmailInbox(
      userTLI.email,
      `*ประกาศนียบัตร${certificate.refName}*`,
      timestamp,
    );
    expect(email.html).toContain(courseVersion.name);
    expect(email.html).toContain(certificate.refName);
  });
});

test.describe('Admin - Course Management', () => {
  test.beforeEach(async ({ configuration, loginPage }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    await loginPage.fillUsername(organizationAdmin.username);
    await loginPage.fillPassword(organizationAdmin.password);
    await loginPage.submit(false);
  });

  test('@SKL-T20189 Admin เข้าถึงหน้าหลักสูตรทั้งหมด, แสดงหน้าตารางรายการหลักสูตรทั้งหมด', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
  }) => {
    await organizationAdminDashboardPage.accessManageCourseList();
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
  });

  test('@SKL-T20192 Admin สร้างหลักสูตรการอบรมวิชาชีพการลงทุน (TSI) และกรอกข้อมูลถูกต้อง, สร้างหลักสูตรสำเร็จ', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCurriculumPage,
    manageCourseDetailPage,
    coursesRepo,
    certificatesRepo,
  }) => {
    const courseName = 'AUTOMATE_CREATE_TSI';
    const courseCode = 'AUTOMATE_TSI';
    const refCodeCerti = 'AUTO_CERTI_REFCODE_TSI';
    const refNameCerti = 'AUTO_CERTI_REFNAME_TSI';
    await coursesRepo.deleteCourseByCode(courseCode);
    await certificatesRepo.deleteCertificateByRefName(refNameCerti);

    await organizationAdminDashboardPage.accessManageCourseList();
    await manageCourseListPage.createCourse();

    // Create TSI course using OIC flow and then modify to TSI
    await manageCourseListPage.createCourseForm.selectObjectiveCourse('อบรมวิชาชีพประกัน (คปภ.)');
    await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
    await manageCourseListPage.createCourseForm.fillCourseName(courseName);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

    // Create Chapter
    await manageCourseDetailPage.accessTabCurriculum();
    await manageCurriculumPage.editCurriculum();
    await manageCurriculumPage.clickCreatePart();
    await manageCurriculumPage.fillNamePart('บทที่1');
    await manageCurriculumPage.clickCreateButton();

    // Create Article
    await manageCurriculumPage.selectCreateChapter('Article');
    await manageCurriculumPage.articleItemElementsPage.fillNameArticle('ทดสอบสร้างบทความ TSI');
    await manageCurriculumPage.articleItemElementsPage.submitSaveButton();
    await manageCurriculumPage.articleItemElementsPage.fillArticleContent(
      'เนื้อหาการอบรมวิชาชีพการลงทุน เพื่อให้ผู้เรียนได้รับความรู้ด้านการลงทุน',
    );
    await manageCurriculumPage.articleItemElementsPage.submitEnableButton();
    await manageCurriculumPage.articleItemElementsPage.submitSaveButton();
    await manageCurriculumPage.clickCompleteButton();

    // Manage Course Features for TSI
    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessEditCourseFeatures();
    await manageCourseDetailPage.generalCourseDetailElementsPage.selectTrainingCenter();
    await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseRenewal('ขอรับใบอนุญาต');
    await manageCourseDetailPage.generalCourseDetailElementsPage.selectApplicantType('ตัวแทน');
    await manageCourseDetailPage.submitSaveButton();

    await manageCurriculumPage.clickPublishContentButton();
    await manageCurriculumPage.verifyCourseStatus('Enable');

    await coursesRepo.deleteCourseByCode(courseCode);
  });

  test('@SKL-T20199 Admin แก้ไขข้อมูลคุณสมบัติหลักสูตรของหลักสูตรการอบรมวิชาชีพการลงทุน (TSI) กรณีกรอกข้อมูลถูกต้อง, สามารถแก้ไขได้และบันทึกสำเร็จ', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCourseDetailPage,
  }) => {
    const course = configuration.shareCourses.cpdTsiCourse1;

    await organizationAdminDashboardPage.accessManageCourseList();
    await manageCourseListPage.searchCourseName(course.courseVersions[1].name);
    await manageCourseListPage.courseEdit();

    // Edit course properties
    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessEditCourseFeatures();
    await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseRenewal('ขอรับใบอนุญาต');
    await manageCourseDetailPage.generalCourseDetailElementsPage.selectApplicantType('ตัวแทน');
    await manageCourseDetailPage.submitSaveButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('บันทึกข้อมูลสำเร็จ');
  });

  test('@SKL-T20201 Admin แก้ไขข้อมูลคุณสมบัติหลักสูตรของหลักสูตรการอบรมวิชาชีพการลงทุน (TSI) กรณีกรอกไม่ข้อมูลถูกต้อง, แสดง error และไม่สามารถบันทึกได้', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCourseDetailPage,
  }) => {
    const course = configuration.shareCourses.cpdTsiCourse1;

    await organizationAdminDashboardPage.accessManageCourseList();
    await manageCourseListPage.searchCourseName(course.courseVersions[1].name);
    await manageCourseListPage.courseEdit();

    // Try to edit with invalid data (leaving required fields empty)
    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessEditCourseFeatures();
    // Clear required fields to trigger validation error
    await manageCourseDetailPage.generalCourseDetailElementsPage.licenseRenewalLocator.clear();
    await manageCourseDetailPage.submitSaveButton();

    // Verify error message appears and save is prevented
    await expect(manageCourseDetailPage.page.locator('.ant-form-item-explain-error')).toBeVisible();
  });

  test('@SKL-T20253 Admin ลบหลักสูตรแบบร่างเวอร์ชัน 1 ที่ sync มาจาก 3rd party, ไม่แสดงปุ่มลบ', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCourseDetailPage,
  }) => {
    // Assuming we have a course synced from 3rd party in draft version 1
    const syncedCourse = configuration.shareCourses.courseSyncB2C;

    await organizationAdminDashboardPage.accessManageCourseList();
    await manageCourseListPage.searchCourseName(syncedCourse.name);
    await manageCourseListPage.courseEdit();

    // Verify delete button is not visible for synced course
    await expect(manageCourseDetailPage.page.locator('button:has-text("ลบ")')).not.toBeVisible();
  });

  test('@SKL-T20205 Admin แก้ไขผู้สอนหลักสูตรการอบรม ที่ sync มาจาก 3rd party, ไม่มีปุ่มแก้ไขและไม่สามารถแก้ไขได้', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCourseDetailPage,
  }) => {
    const syncedCourse = configuration.shareCourses.courseSyncB2C;

    await organizationAdminDashboardPage.accessManageCourseList();
    await manageCourseListPage.searchCourseName(syncedCourse.name);
    await manageCourseListPage.courseEdit();

    // Navigate to instructor section
    await manageCourseDetailPage.accessTabGeneralInfo();

    // Verify instructor edit button is not available for synced course
    await expect(manageCourseDetailPage.page.locator('button:has-text("แก้ไขผู้สอน")')).not.toBeVisible();
  });

  test('@SKL-T20196 Admin แก้ไขข้อมูลภาพรวมของหลักสูตรการอบรม ที่ sync มาจาก 3rd party กรณีกรอกข้อมูลถูกต้อง, สามารถแก้ไขได้', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCourseDetailPage,
  }) => {
    const syncedCourse = configuration.shareCourses.courseSyncB2C;

    await organizationAdminDashboardPage.accessManageCourseList();
    await manageCourseListPage.searchCourseName(syncedCourse.name);
    await manageCourseListPage.courseEdit();

    // Edit course overview/description
    await manageCourseDetailPage.accessTabGeneralInfo();
    const newDescription = 'Updated course description for synced course';

    // Verify that overview editing is allowed for synced courses
    await manageCourseDetailPage.page.locator('textarea[placeholder*="คำอธิบาย"]').fill(newDescription);
    await manageCourseDetailPage.submitSaveButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('บันทึกข้อมูลสำเร็จ');
  });
});
