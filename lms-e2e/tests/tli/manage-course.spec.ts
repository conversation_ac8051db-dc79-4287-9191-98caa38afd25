import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';
import { CertificateType, LicenseType } from '../../elements/pages/backoffice/certificate-course-detail-elements';
import { ObjectiveCourse } from '../../elements/pages/backoffice/create-course-elements';
import {
  ApplicantType,
  IsDeduct,
  LicenseRenewal,
} from '../../elements/pages/backoffice/general-course-detail-elements';
import { CourseStatus, MediaType } from '../../elements/pages/backoffice/curriculum/manage-curriculum-page';
import { QuizType } from '../../elements/pages/backoffice/curriculum/quiz-item-elements-page';

test.describe('Positive', () => {
  test.beforeAll(
    async ({
      usersRepo,
      enrollmentsRepo,
      enrollmentCertificatesRepo,
      enrollmentAttachmentsRepo,
      quizAnswersRepo,
      configuration,
    }) => {
      const lmsUser = await usersRepo.getByEmail(configuration.usersLocal.userTLI1.email);
      const userEnrollments = await enrollmentsRepo.getAllEnrollmentsForUser(lmsUser.guid);
      for (const userEnrollment of userEnrollments) {
        await enrollmentCertificatesRepo.deleteAllEnrollmentCertificatesForEnrollment(userEnrollment.id);
        await enrollmentAttachmentsRepo.deleteAllEnrollmentAttachedForEnrollment(userEnrollment.id);
        await quizAnswersRepo.deleteManyByEnrollmentId(userEnrollment.id);
      }
      await enrollmentsRepo.deleteAllEnrollmentsForUser(lmsUser.guid);
    },
  );

  test.describe('Smoke', () => {
    test.beforeEach(async ({ configuration, loginPage }) => {
      const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
      await loginPage.fillUsername(organizationAdmin.username);
      await loginPage.fillPassword(organizationAdmin.password);
      await loginPage.submit(false);
    });
    test('@SKL-T20193 @SKL-T20370 @SKL-T20279 @SKL-T20301 @SKL-T20303 @SKL-T20305 Admin creates new self-enrollment Regular course without a certificate', async ({
      configuration,
      loginPage,
      homePage,
      catalogPage,
      manageCourseListPage,
      organizationAdminDashboardPage,
      manageCurriculumPage,
      courseDetailPage,
      learningPage,
      coursesRepo,
      manageCourseDetailPage,
      courseVersionsRepo,
    }) => {
      const user = configuration.usersLocal.userTLI1;
      const courseName = 'Automate Test Create Course Regular';
      const courseCode = 'AUTOMATE_REGULAR';
      await courseVersionsRepo.deleteByCourseName(courseName);
      await coursesRepo.deleteCourseByCode(courseCode);

      await organizationAdminDashboardPage.accessManageCourseList();
      await manageCourseListPage.createCourse();

      await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.Regular);
      await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
      await manageCourseListPage.createCourseForm.fillCourseName(courseName);
      await manageCourseListPage.createCourseForm.submitCreateCourseButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

      // Create Chapter
      await manageCourseDetailPage.accessTabCurriculum();
      await manageCurriculumPage.editCurriculum();
      await manageCurriculumPage.clickCreatePart();
      await manageCurriculumPage.fillNamePart('บทที่1');
      await manageCurriculumPage.clickCreateButton();

      // Create Article
      await manageCurriculumPage.selectCreateChapter(MediaType.Article);
      await manageCurriculumPage.articleItemElementsPage.fillNameArticle('ทดสอบสร้างบทความ');
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();
      await manageCurriculumPage.articleItemElementsPage.fillArticleContent(
        'กอรกเนื้อหา เพื่อให้ผู้เรียนได้อ่านบทความ ได้รับความรู้',
      );
      await manageCurriculumPage.articleItemElementsPage.submitEnableButton();
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();

      await manageCurriculumPage.clickCompleteButton();
      await manageCurriculumPage.clickPublishContentButton();
      await manageCurriculumPage.verifyCourseStatus(CourseStatus.Enable);

      // User Learning
      await homePage.logout();
      await loginPage.fillUsername(user.username);
      await loginPage.fillPassword(user.password);
      await loginPage.submit(false);
      await homePage.searchCourse(courseName);
      await catalogPage.clickCourseCard(false, 'Regular', courseName, false);
      await courseDetailPage.clickSelfEnroll();
      expect(await learningPage.verifyCompleteLearning()).toBeTruthy();
      await learningPage.clickConfirmCompleteLearning();
      await learningPage.verifyChapterCompleted(0);

      await coursesRepo.deleteCourseByCode(courseCode);
    });

    test.skip('SKL-T20191 @SKL-T20370 @SKL-T20301 @SKL-T20303 @SKL-T20305 @SKL-T20307 @SKL-T20309 @SKL-T20315 Admin create new OIC course with certificate and pass criteria', async ({
      // skip wait for fix [SKL-16772]
      configuration,
      loginPage,
      homePage,
      catalogPage,
      manageCourseListPage,
      organizationAdminDashboardPage,
      manageCurriculumPage,
      manageCourseDetailPage,
      coursesRepo,
      courseDetailPage,
      certificatesRepo,
    }) => {
      const user = configuration.usersLocal.userTLI1;
      const courseName = 'AUTOMATE_CREATE_OIC';
      const courseCode = 'AUTOMATE_OIC';
      const refCodeCerti = 'AUTO_CERTI_REFCODE';
      const refNameCerti = 'AUTO_CERTI_REFNAME';
      await coursesRepo.deleteCourseByCode(courseCode);
      await certificatesRepo.deleteCertificateByRefName(refNameCerti);

      await organizationAdminDashboardPage.accessManageCourseList();
      await manageCourseListPage.createCourse();
      await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.OIC);
      await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
      await manageCourseListPage.createCourseForm.fillCourseName(courseName);
      await manageCourseListPage.createCourseForm.submitCreateCourseButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

      // Create Chapter
      await manageCourseDetailPage.accessTabCurriculum();
      await manageCurriculumPage.editCurriculum();
      await manageCurriculumPage.clickCreatePart();
      await manageCurriculumPage.fillNamePart('บทที่1');
      await manageCurriculumPage.clickCreateButton();

      // Create Article
      await manageCurriculumPage.selectCreateChapter(MediaType.Article);
      await manageCurriculumPage.articleItemElementsPage.fillNameArticle('ทดสอบสร้างบทความ');
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();
      await manageCurriculumPage.articleItemElementsPage.fillArticleContent(
        'กอรกเนื้อหา เพื่อให้ผู้เรียนได้อ่านบทความ ได้รับความรู้',
      );
      await manageCurriculumPage.articleItemElementsPage.submitEnableButton();
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();

      // Create Survey
      await manageCurriculumPage.selectCreateChapter(MediaType.Survey);
      await manageCurriculumPage.surveyItemElementsPage.fillSurveyName('ทดสอบสร้างแบบสอบถาม');
      await manageCurriculumPage.surveyItemElementsPage.submitSaveButton();
      await manageCurriculumPage.surveyItemElementsPage.fillMainSurveyName('แบบทดสอบหลัก');
      await manageCurriculumPage.surveyItemElementsPage.clickGenerateSurveyCode();
      await manageCurriculumPage.surveyItemElementsPage.clickCreateSurvey();
      await manageCurriculumPage.surveyItemElementsPage.clickSubmitSurveySaveButton();
      await manageCurriculumPage.surveyItemElementsPage.submitEnableButton();
      await manageCurriculumPage.surveyItemElementsPage.submitSaveButton();

      // Create Quiz
      await manageCurriculumPage.selectCreateChapter(MediaType.Quiz);
      await manageCurriculumPage.quizItemCurriculumPage.fillQuizName('ทดสอบสร้างแบบทดสอบ');
      await manageCurriculumPage.quizItemCurriculumPage.submitSaveButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillMainQuizName('แบบทดสอบหลัก');
      await manageCurriculumPage.quizItemCurriculumPage.selectQuizType(QuizType.Regular);
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateNewQuestionTopicQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillQuestionTopicQuiz('ตั้งคำถาม ตอบ A');
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ A', 1);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ B', 2);
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateNewQuestionTopicQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillQuestionTopicQuiz('ตั้งคำถาม ตอบ CC');
      await manageCurriculumPage.quizItemCurriculumPage.clickAddNewAnswerButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ AA', 1);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ BB', 2);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ CC', 3);
      await manageCurriculumPage.quizItemCurriculumPage.selectChoiceAnswer('C');
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.submitEnableButton();
      await manageCurriculumPage.quizItemCurriculumPage.submitSaveButton();
      await manageCurriculumPage.clickCompleteButton();

      // Manage Course Features
      await manageCourseDetailPage.accessTabGeneralInfo();
      await manageCourseDetailPage.accessEditCourseFeatures();
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectTrainingCenter();
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseRenewal(LicenseRenewal.TSI);
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectApplicantType(ApplicantType.Agent);
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseType(LicenseType.Life);
      await manageCourseDetailPage.submitSaveButton();

      // Manage Certificate
      await manageCourseDetailPage.accessMenuCertificate();
      await manageCourseDetailPage.accessEditCertificate();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickAddNewCertificate();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.selectCertificateType(CertificateType.TSI);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillRefCodeCertificate(refCodeCerti);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillRefNameCertificate(refNameCerti);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.submitSaveCertificateButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('บันทึกข้อมูลสำเร็จ');
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickEnableCertificateButton();
      await manageCourseDetailPage.clickCompleteButton();

      // Manage Criteria
      await manageCourseDetailPage.accessMenuStudyCriteriaMenuLocator();
      await manageCourseDetailPage.accessEditCriteria();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickEnablePassScoreCriteria();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillPassScoreCriteria('2', 1);
      await manageCourseDetailPage.submitSaveButton();
      await manageCurriculumPage.clickPublishContentButton();
      await manageCurriculumPage.verifyCourseStatus(CourseStatus.Enable);

      // User Learning
      await homePage.logout();
      await loginPage.fillUsername(user.username);
      await loginPage.fillPassword(user.password);
      await loginPage.submit(false);
      await homePage.searchCourse(courseName);
      await catalogPage.clickCourseCard(true, 'OIC', courseName, true);
      expect(await courseDetailPage.getObjectiveCourse()).toContain('อบรมวิชาชีพประกัน (OIC)');
      expect(await courseDetailPage.getCourseName()).toContain(courseName);
      await courseDetailPage.accessCurriculumDetail();
      expect(await courseDetailPage.getAllCurriculumDetail()).toContain('ทดสอบสร้างบทความ');
      expect(await courseDetailPage.getAllCurriculumDetail()).toContain('ทดสอบสร้างแบบสอบถาม');
      expect(await courseDetailPage.getAllCurriculumDetail()).toContain('ทดสอบสร้างแบบทดสอบ');

      await coursesRepo.deleteCourseByCode(courseCode);
    });

    test('@SKL-T20370 @SKL-T20307 @SKL-T20309 @SKL-T20315 Admin creates new OIC-4 course without reducing training hours', async ({
      configuration,
      manageCourseListPage,
      organizationAdminDashboardPage,
      manageCurriculumPage,
      manageCourseDetailPage,
      coursesRepo,
      certificatesRepo,
    }) => {
      const user = configuration.usersLocal.userTLI1;
      const courseName = 'AUTOMATE_CREATE_OIC4';
      const courseCode = 'AUTOMATE_OIC4';
      const refCodeCerti = 'AUTO_CERTI_REFCODE4';
      const refNameCerti = 'AUTO_CERTI_REFNAME4';
      await coursesRepo.deleteCourseByCode(courseCode);
      await certificatesRepo.deleteCertificateByRefName(refNameCerti);

      await organizationAdminDashboardPage.accessManageCourseList();
      await manageCourseListPage.createCourse();
      await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.OIC);
      await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
      await manageCourseListPage.createCourseForm.fillCourseName(courseName);
      await manageCourseListPage.createCourseForm.submitCreateCourseButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

      // Create Chapter
      await manageCourseDetailPage.accessTabCurriculum();
      await manageCurriculumPage.editCurriculum();
      await manageCurriculumPage.clickCreatePart();
      await manageCurriculumPage.fillNamePart('บทที่1');
      await manageCurriculumPage.clickCreateButton();

      // Create Article
      await manageCurriculumPage.selectCreateChapter(MediaType.Article);
      await manageCurriculumPage.articleItemElementsPage.fillNameArticle('ทดสอบสร้างบทความ');
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();
      await manageCurriculumPage.articleItemElementsPage.fillArticleContent(
        'กอรกเนื้อหา เพื่อให้ผู้เรียนได้อ่านบทความ ได้รับความรู้',
      );
      await manageCurriculumPage.articleItemElementsPage.submitEnableButton();
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();

      // Create Survey
      await manageCurriculumPage.selectCreateChapter(MediaType.Survey);
      await manageCurriculumPage.surveyItemElementsPage.fillSurveyName('ทดสอบสร้างแบบสอบถาม');
      await manageCurriculumPage.surveyItemElementsPage.submitSaveButton();
      await manageCurriculumPage.surveyItemElementsPage.fillMainSurveyName('แบบทดสอบหลัก');
      await manageCurriculumPage.surveyItemElementsPage.clickGenerateSurveyCode();
      await manageCurriculumPage.surveyItemElementsPage.clickCreateSurvey();
      await manageCurriculumPage.surveyItemElementsPage.clickSubmitSurveySaveButton();
      await manageCurriculumPage.surveyItemElementsPage.submitEnableButton();
      await manageCurriculumPage.surveyItemElementsPage.submitSaveButton();

      // Create Quiz
      await manageCurriculumPage.selectCreateChapter(MediaType.Quiz);
      await manageCurriculumPage.quizItemCurriculumPage.fillQuizName('ทดสอบสร้างแบบทดสอบ');
      await manageCurriculumPage.quizItemCurriculumPage.submitSaveButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillMainQuizName('แบบทดสอบหลัก');
      await manageCurriculumPage.quizItemCurriculumPage.selectQuizType(QuizType.Regular);
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateNewQuestionTopicQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillQuestionTopicQuiz('ตั้งคำถาม ตอบ A');
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ A', 1);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ B', 2);
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateNewQuestionTopicQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillQuestionTopicQuiz('ตั้งคำถาม ตอบ CC');
      await manageCurriculumPage.quizItemCurriculumPage.clickAddNewAnswerButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ AA', 1);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ BB', 2);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ CC', 3);
      await manageCurriculumPage.quizItemCurriculumPage.selectChoiceAnswer('C');
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.submitEnableButton();
      await manageCurriculumPage.quizItemCurriculumPage.submitSaveButton();
      await manageCurriculumPage.clickCompleteButton();

      // Manage Course Features
      await manageCourseDetailPage.accessTabGeneralInfo();
      await manageCourseDetailPage.accessEditCourseFeatures();
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectTrainingCenter();
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseRenewal(LicenseRenewal.OIC4);
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectIsDeduct(IsDeduct.nonDeduct);
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectApplicantType(ApplicantType.Agent);
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseType(LicenseType.Life);
      await manageCourseDetailPage.submitSaveButton();

      // Manage Certificate
      await manageCourseDetailPage.accessMenuCertificate();
      await manageCourseDetailPage.accessEditCertificate();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickAddNewCertificate();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.selectCertificateType(CertificateType.OIC4);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillRefCodeCertificate(refCodeCerti);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillRefNameCertificate(refNameCerti);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.submitSaveCertificateButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('บันทึกข้อมูลสำเร็จ');
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickEnableCertificateButton();
      await manageCourseDetailPage.clickCompleteButton();

      // Manage Criteria
      await manageCourseDetailPage.accessMenuStudyCriteriaMenuLocator();
      await manageCourseDetailPage.accessEditCriteria();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickEnablePassScoreCriteria();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillPassScoreCriteria('2', 1);
      await manageCourseDetailPage.submitSaveButton();
      await manageCurriculumPage.clickPublishContentButton();
      await manageCurriculumPage.verifyCourseStatus(CourseStatus.Enable);

      await coursesRepo.deleteCourseByCode(courseCode);
    });

    test('@SKL-T20370 Admin creates new OIC-4 course with reduced training hours and not required document', async ({
      configuration,
      manageCourseListPage,
      organizationAdminDashboardPage,
      manageCurriculumPage,
      manageCourseDetailPage,
      coursesRepo,
      certificatesRepo,
    }) => {
      const user = configuration.usersLocal.userTLI1;
      const courseName = 'AUTOMATE_CREATE_OIC4_NOT_REQUIRED_DOC';
      const courseCode = 'AUTOMATE_OIC4_NON_DO';
      const refCodeCerti = 'AUTO_CERTI_REFCODE4_NON_DOCUMENT';
      const refNameCerti = 'AUTO_CERTI_REFNAME4_NON_DOCUMENT';
      await coursesRepo.deleteCourseByCode(courseCode);
      await certificatesRepo.deleteCertificateByRefName(refNameCerti);

      await organizationAdminDashboardPage.accessManageCourseList();
      await manageCourseListPage.createCourse();
      await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.OIC);
      await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
      await manageCourseListPage.createCourseForm.fillCourseName(courseName);
      await manageCourseListPage.createCourseForm.submitCreateCourseButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

      // Create Chapter
      await manageCourseDetailPage.accessTabCurriculum();
      await manageCurriculumPage.editCurriculum();
      await manageCurriculumPage.clickCreatePart();
      await manageCurriculumPage.fillNamePart('บทที่1');
      await manageCurriculumPage.clickCreateButton();

      // Create Article
      await manageCurriculumPage.selectCreateChapter(MediaType.Article);
      await manageCurriculumPage.articleItemElementsPage.fillNameArticle('ทดสอบสร้างบทความ');
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();
      await manageCurriculumPage.articleItemElementsPage.fillArticleContent(
        'กอรกเนื้อหา เพื่อให้ผู้เรียนได้อ่านบทความ ได้รับความรู้',
      );
      await manageCurriculumPage.articleItemElementsPage.submitEnableButton();
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();

      // Create Survey
      await manageCurriculumPage.selectCreateChapter(MediaType.Survey);
      await manageCurriculumPage.surveyItemElementsPage.fillSurveyName('ทดสอบสร้างแบบสอบถาม');
      await manageCurriculumPage.surveyItemElementsPage.submitSaveButton();
      await manageCurriculumPage.surveyItemElementsPage.fillMainSurveyName('แบบทดสอบหลัก');
      await manageCurriculumPage.surveyItemElementsPage.clickGenerateSurveyCode();
      await manageCurriculumPage.surveyItemElementsPage.clickCreateSurvey();
      await manageCurriculumPage.surveyItemElementsPage.clickSubmitSurveySaveButton();
      await manageCurriculumPage.surveyItemElementsPage.submitEnableButton();
      await manageCurriculumPage.surveyItemElementsPage.submitSaveButton();

      // Create Quiz
      await manageCurriculumPage.selectCreateChapter(MediaType.Quiz);
      await manageCurriculumPage.quizItemCurriculumPage.fillQuizName('ทดสอบสร้างแบบทดสอบ');
      await manageCurriculumPage.quizItemCurriculumPage.submitSaveButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillMainQuizName('แบบทดสอบหลัก');
      await manageCurriculumPage.quizItemCurriculumPage.selectQuizType(QuizType.Regular);
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateNewQuestionTopicQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillQuestionTopicQuiz('ตั้งคำถาม ตอบ A');
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ A', 1);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ B', 2);
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateNewQuestionTopicQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillQuestionTopicQuiz('ตั้งคำถาม ตอบ CC');
      await manageCurriculumPage.quizItemCurriculumPage.clickAddNewAnswerButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ AA', 1);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ BB', 2);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ CC', 3);
      await manageCurriculumPage.quizItemCurriculumPage.selectChoiceAnswer('C');
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.submitEnableButton();
      await manageCurriculumPage.quizItemCurriculumPage.submitSaveButton();
      await manageCurriculumPage.clickCompleteButton();

      // Manage Course Features
      await manageCourseDetailPage.accessTabGeneralInfo();
      await manageCourseDetailPage.accessEditCourseFeatures();
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectTrainingCenter();
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseRenewal(LicenseRenewal.OIC4);
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectIsDeduct(IsDeduct.deductWithoutDocument);
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectApplicantType(ApplicantType.Agent);
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseType(LicenseType.Life);
      await manageCourseDetailPage.submitSaveButton();

      // Manage Certificate
      await manageCourseDetailPage.accessMenuCertificate();
      await manageCourseDetailPage.accessEditCertificate();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickAddNewCertificate();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.selectCertificateType(CertificateType.OIC4);

      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillRefCodeCertificate(refCodeCerti);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillRefNameCertificate(refNameCerti);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.submitSaveCertificateButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('บันทึกข้อมูลสำเร็จ');
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickEnableCertificateButton();
      await manageCourseDetailPage.clickCompleteButton();

      // Manage Criteria
      await manageCourseDetailPage.accessMenuStudyCriteriaMenuLocator();
      await manageCourseDetailPage.accessEditCriteria();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickEnablePassScoreCriteria();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillPassScoreCriteria('2', 1);
      await manageCourseDetailPage.submitSaveButton();
      await manageCurriculumPage.clickPublishContentButton();
      await manageCurriculumPage.verifyCourseStatus(CourseStatus.Enable);

      await coursesRepo.deleteCourseByCode(courseCode);
    });

    test.skip('@SKL-T20370 Admin creates new UL course with enable repeat study', async ({
      // skip wait for fix [SKL-16772]
      configuration,
      loginPage,
      homePage,
      catalogPage,
      manageCourseListPage,
      organizationAdminDashboardPage,
      manageCurriculumPage,
      manageCourseDetailPage,
      coursesRepo,
      courseDetailPage,
    }) => {
      const user = configuration.usersLocal.userTLI1;
      const courseName = 'AUTOMATE_CREATE_UL';
      const courseCode = 'AUTOMATE_UL';
      const refCodeCerti = 'AUTO_CERTI_REFCODE_UL';
      const refNameCerti = 'AUTO_CERTI_REFNAME_UL';
      await coursesRepo.deleteCourseByCode(courseCode);

      await organizationAdminDashboardPage.accessManageCourseList();
      await manageCourseListPage.createCourse();
      await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.OIC);
      await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
      await manageCourseListPage.createCourseForm.fillCourseName(courseName);
      await manageCourseListPage.createCourseForm.submitCreateCourseButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

      // Create Chapter
      await manageCourseDetailPage.accessTabCurriculum();
      await manageCurriculumPage.editCurriculum();
      await manageCurriculumPage.clickCreatePart();
      await manageCurriculumPage.fillNamePart('บทที่1');
      await manageCurriculumPage.clickCreateButton();

      // Create Article
      await manageCurriculumPage.selectCreateChapter(MediaType.Article);
      await manageCurriculumPage.articleItemElementsPage.fillNameArticle('ทดสอบสร้างบทความ');
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();
      await manageCurriculumPage.articleItemElementsPage.fillArticleContent(
        'กรอกเนื้อหา เพื่อให้ผู้เรียนได้อ่านบทความ ได้รับความรู้',
      );
      await manageCurriculumPage.articleItemElementsPage.submitEnableButton();
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();

      // Create Survey
      await manageCurriculumPage.selectCreateChapter(MediaType.Survey);
      await manageCurriculumPage.surveyItemElementsPage.fillSurveyName('ทดสอบสร้างแบบสอบถาม');
      await manageCurriculumPage.surveyItemElementsPage.submitSaveButton();
      await manageCurriculumPage.surveyItemElementsPage.fillMainSurveyName('แบบทดสอบหลัก');
      await manageCurriculumPage.surveyItemElementsPage.clickGenerateSurveyCode();
      await manageCurriculumPage.surveyItemElementsPage.clickCreateSurvey();
      await manageCurriculumPage.surveyItemElementsPage.clickSubmitSurveySaveButton();
      await manageCurriculumPage.surveyItemElementsPage.submitEnableButton();
      await manageCurriculumPage.surveyItemElementsPage.submitSaveButton();

      // Create Quiz
      await manageCurriculumPage.selectCreateChapter(MediaType.Quiz);
      await manageCurriculumPage.quizItemCurriculumPage.fillQuizName('ทดสอบสร้างแบบทดสอบ');
      await manageCurriculumPage.quizItemCurriculumPage.submitSaveButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillMainQuizName('แบบทดสอบหลัก');
      await manageCurriculumPage.quizItemCurriculumPage.selectQuizType(QuizType.Regular);
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateNewQuestionTopicQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillQuestionTopicQuiz('ตั้งคำถาม ตอบ A');
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ A', 1);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ B', 2);
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateNewQuestionTopicQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillQuestionTopicQuiz('ตั้งคำถาม ตอบ CC');
      await manageCurriculumPage.quizItemCurriculumPage.clickAddNewAnswerButton();
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ AA', 1);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ BB', 2);
      await manageCurriculumPage.quizItemCurriculumPage.fillAnswer('คำตอบ CC', 3);
      await manageCurriculumPage.quizItemCurriculumPage.selectChoiceAnswer('C');
      await manageCurriculumPage.quizItemCurriculumPage.clickCreateQuizButton();
      await manageCurriculumPage.quizItemCurriculumPage.submitEnableButton();
      await manageCurriculumPage.quizItemCurriculumPage.submitSaveButton();
      await manageCurriculumPage.clickCompleteButton();

      // Manage Course Features
      await manageCourseDetailPage.accessTabGeneralInfo();
      await manageCourseDetailPage.accessEditCourseFeatures();
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectTrainingCenter();
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseRenewal(LicenseRenewal.UL);
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectApplicantType(ApplicantType.Agent);

      await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseType(LicenseType.Life);
      await manageCourseDetailPage.submitSaveButton();

      // Manage Certificate
      await manageCourseDetailPage.accessMenuCertificate();
      await manageCourseDetailPage.accessEditCertificate();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickAddNewCertificate();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.selectCertificateType(CertificateType.UL);

      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillRefCodeCertificate(refCodeCerti);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillRefNameCertificate(refNameCerti);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.submitSaveCertificateButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('บันทึกข้อมูลสำเร็จ');
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickEnableCertificateButton();
      await manageCourseDetailPage.clickCompleteButton();

      // Manage Criteria
      await manageCourseDetailPage.accessMenuStudyCriteriaMenuLocator();
      await manageCourseDetailPage.accessEditCriteria();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickEnablePassScoreCriteria();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillPassScoreCriteria('2', 1);
      await manageCourseDetailPage.submitSaveButton();
      await manageCurriculumPage.clickPublishContentButton();
      await manageCurriculumPage.verifyCourseStatus(CourseStatus.Enable);

      // Manage Re-enroll
      await manageCourseDetailPage.accessMenuEnroll();
      await manageCourseDetailPage.accessEditEnroll();
      await manageCourseDetailPage.accessEnableReEnroll();
      await manageCourseDetailPage.submitSaveButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('บันทึกข้อมูลสำเร็จ');

      // User Learning
      await homePage.logout();
      await loginPage.fillUsername(user.username);
      await loginPage.fillPassword(user.password);
      await loginPage.submit(false);
      await homePage.searchCourse(courseName);
      await catalogPage.clickCourseCard(true, 'OIC', courseName, true);
      expect(await courseDetailPage.getObjectiveCourse()).toContain('อบรมวิชาชีพประกัน (OIC)');
      expect(await courseDetailPage.getCourseName()).toContain(courseName);
      await courseDetailPage.accessCurriculumDetail();
      expect(await courseDetailPage.getAllCurriculumDetail()).toContain('ทดสอบสร้างบทความ');
      expect(await courseDetailPage.getAllCurriculumDetail()).toContain('ทดสอบสร้างแบบสอบถาม');
      expect(await courseDetailPage.getAllCurriculumDetail()).toContain('ทดสอบสร้างแบบทดสอบ');

      await coursesRepo.deleteCourseByCode(courseCode);
    });

    test('@SKL-T20189 Admin accesses all courses page and displays course list table', async ({
      configuration,
      manageCourseListPage,
      organizationAdminDashboardPage,
    }) => {
      await organizationAdminDashboardPage.accessManageCourseList();
      await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
      await expect(manageCourseListPage.searchInputLocator).toBeVisible();
      await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
      await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
    });

    test('@SKL-T20192 Admin creates TSI investment professional training course with valid data successfully', async ({
      configuration,
      manageCourseListPage,
      organizationAdminDashboardPage,
      manageCurriculumPage,
      manageCourseDetailPage,
      coursesRepo,
      certificatesRepo,
    }) => {
      const courseName = 'AUTOMATE_CREATE_TSI';
      const courseCode = 'AUTOMATE_TSI';
      const refCodeCerti = 'AUTO_CERTI_REFCODE_TSI';
      const refNameCerti = 'AUTO_CERTI_REFNAME_TSI';
      await coursesRepo.deleteCourseByCode(courseCode);
      await certificatesRepo.deleteCertificateByRefName(refNameCerti);

      await organizationAdminDashboardPage.accessManageCourseList();
      await manageCourseListPage.createCourse();
      await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.OIC);
      await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
      await manageCourseListPage.createCourseForm.fillCourseName(courseName);
      await manageCourseListPage.createCourseForm.submitCreateCourseButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

      // Create Chapter
      await manageCourseDetailPage.accessTabCurriculum();
      await manageCurriculumPage.editCurriculum();
      await manageCurriculumPage.clickCreatePart();
      await manageCurriculumPage.fillNamePart('บทที่1');
      await manageCurriculumPage.clickCreateButton();

      // Create Article
      await manageCurriculumPage.selectCreateChapter(MediaType.Article);
      await manageCurriculumPage.articleItemElementsPage.fillNameArticle('ทดสอบสร้างบทความ TSI');
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();
      await manageCurriculumPage.articleItemElementsPage.fillArticleContent(
        'เนื้อหาการอบรมวิชาชีพการลงทุน เพื่อให้ผู้เรียนได้รับความรู้ด้านการลงทุน',
      );
      await manageCurriculumPage.articleItemElementsPage.submitEnableButton();
      await manageCurriculumPage.articleItemElementsPage.submitSaveButton();
      await manageCurriculumPage.clickCompleteButton();

      // Manage Course Features for TSI
      await manageCourseDetailPage.accessTabGeneralInfo();
      await manageCourseDetailPage.accessEditCourseFeatures();
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectTrainingCenter();
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseRenewal(LicenseRenewal.TSI);
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectApplicantType(ApplicantType.Agent);
      await manageCourseDetailPage.submitSaveButton();

      // Manage Certificate for TSI
      await manageCourseDetailPage.accessMenuCertificate();
      await manageCourseDetailPage.accessEditCertificate();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickAddNewCertificate();
      await manageCourseDetailPage.certificateCourseDetailElementsPage.selectCertificateType(CertificateType.TSI);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillRefCodeCertificate(refCodeCerti);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.fillRefNameCertificate(refNameCerti);
      await manageCourseDetailPage.certificateCourseDetailElementsPage.submitSaveCertificateButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('บันทึกข้อมูลสำเร็จ');
      await manageCourseDetailPage.certificateCourseDetailElementsPage.clickEnableCertificateButton();
      await manageCourseDetailPage.clickCompleteButton();

      await manageCurriculumPage.clickPublishContentButton();
      await manageCurriculumPage.verifyCourseStatus(CourseStatus.Enable);

      await coursesRepo.deleteCourseByCode(courseCode);
    });

    test('@SKL-T20199 Admin edits TSI course properties with valid data, can edit and save successfully', async ({
      configuration,
      manageCourseListPage,
      organizationAdminDashboardPage,
      manageCourseDetailPage,
      coursesRepo,
    }) => {
      const course = configuration.shareCourses.cpdTsiCourse1;

      await organizationAdminDashboardPage.accessManageCourseList();
      await manageCourseListPage.searchCourseName(course.courseVersions[1].name);
      await manageCourseListPage.courseEdit();

      // Edit course properties
      await manageCourseDetailPage.accessTabGeneralInfo();
      await manageCourseDetailPage.accessEditCourseFeatures();
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectLicenseRenewal(LicenseRenewal.TSI);
      await manageCourseDetailPage.generalCourseDetailElementsPage.selectApplicantType(ApplicantType.Agent);
      await manageCourseDetailPage.submitSaveButton();
      await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('บันทึกข้อมูลสำเร็จ');
    });

    test('@SKL-T20201 Admin edits TSI course properties with invalid data, shows error and cannot save', async ({
      configuration,
      manageCourseListPage,
      organizationAdminDashboardPage,
      manageCourseDetailPage,
    }) => {
      const course = configuration.shareCourses.cpdTsiCourse1;

      await organizationAdminDashboardPage.accessManageCourseList();
      await manageCourseListPage.searchCourseName(course.courseVersions[1].name);
      await manageCourseListPage.courseEdit();

      // Try to edit with invalid data (leaving required fields empty)
      await manageCourseDetailPage.accessTabGeneralInfo();
      await manageCourseDetailPage.accessEditCourseFeatures();
      // Clear required fields to trigger validation error
      await manageCourseDetailPage.generalCourseDetailElementsPage.licenseRenewalLocator.clear();
      await manageCourseDetailPage.submitSaveButton();

      // Verify error message appears and save is prevented
      await expect(manageCourseDetailPage.page.locator('.ant-form-item-explain-error')).toBeVisible();
    });
  });
});

test.describe('Negative', () => {
  test.beforeEach(async ({ configuration, loginPage }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    await loginPage.fillUsername(organizationAdmin.username);
    await loginPage.fillPassword(organizationAdmin.password);
    await loginPage.submit(false);
  });

  test('@SKL-T20253 Admin cannot delete draft version 1 course synced from 3rd party, delete button not shown', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCourseDetailPage,
  }) => {
    // Assuming we have a course synced from 3rd party in draft version 1
    const syncedCourse = configuration.shareCourses.courseSyncB2C;

    await organizationAdminDashboardPage.accessManageCourseList();
    await manageCourseListPage.searchCourseName(syncedCourse.name);
    await manageCourseListPage.courseEdit();

    // Verify delete button is not visible for synced course
    await expect(manageCourseDetailPage.page.locator('button:has-text("ลบ")')).not.toBeVisible();
  });

  test('@SKL-T20205 Admin cannot edit instructor for training course synced from 3rd party, no edit button and cannot edit', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCourseDetailPage,
  }) => {
    const syncedCourse = configuration.shareCourses.courseSyncB2C;

    await organizationAdminDashboardPage.accessManageCourseList();
    await manageCourseListPage.searchCourseName(syncedCourse.name);
    await manageCourseListPage.courseEdit();

    // Navigate to instructor section
    await manageCourseDetailPage.accessTabGeneralInfo();

    // Verify instructor edit button is not available for synced course
    await expect(manageCourseDetailPage.page.locator('button:has-text("แก้ไขผู้สอน")')).not.toBeVisible();
  });

  test('@SKL-T20196 Admin can edit overview data for training course synced from 3rd party with valid data', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCourseDetailPage,
  }) => {
    const syncedCourse = configuration.shareCourses.courseSyncB2C;

    await organizationAdminDashboardPage.accessManageCourseList();
    await manageCourseListPage.searchCourseName(syncedCourse.name);
    await manageCourseListPage.courseEdit();

    // Edit course overview/description
    await manageCourseDetailPage.accessTabGeneralInfo();
    const newDescription = 'Updated course description for synced course';

    // Verify that overview editing is allowed for synced courses
    await manageCourseDetailPage.page.locator('textarea[placeholder*="คำอธิบาย"]').fill(newDescription);
    await manageCourseDetailPage.submitSaveButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('บันทึกข้อมูลสำเร็จ');
  });
});
